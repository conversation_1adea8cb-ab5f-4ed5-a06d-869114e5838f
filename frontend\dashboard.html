<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard - Mermadic</title>
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/nui/nui.css">
</head>
<body>
  <header>
    <div class="container">
      <h1>Mermadic</h1>
      <nav>
        <ul>
          <li><a href="/">Home</a></li>
          <li><a href="/dashboard.html" class="active">Dashboard</a></li>
          <li><a href="#" id="logout-link">Logout</a></li>
        </ul>
      </nav>
    </div>
  </header>

  <main class="container">
    <section class="dashboard-header">
      <h2>My Diagrams</h2>
      <button id="create-new-chart" class="nui-button primary">Create New Diagram</button>
    </section>

    <!-- View Chart Modal -->
    <div id="view-chart-modal" class="modal" style="display: none;">
      <div class="modal-content">
        <div class="modal-header">
          <h3 id="view-chart-title">View Diagram</h3>
          <span class="close-modal" id="close-view-modal">&times;</span>
        </div>
        <div class="modal-body">
          <div id="view-chart-container" class="view-chart-container"></div>
        </div>
        <div class="modal-footer">
          <button id="view-edit-chart" class="nui-button small">Edit</button>
          <button id="view-delete-chart" class="nui-button small">Delete</button>
          <button id="view-share-chart" class="nui-button small">Share</button>
          <button id="close-view-button" class="nui-button secondary small">Close</button>
        </div>
      </div>
    </div>

    <section id="chart-editor" class="chart-editor" style="display: none;">
      <h3 id="editor-title">Create New Diagram</h3>
      <form id="chart-form">
        <input type="hidden" id="chart-id">
        <div class="form-group">
          <label for="chart-title">Title</label>
          <input type="text" id="chart-title" name="chart-title" class="nui-input" required>
        </div>

        <div class="form-group">
          <label for="chart-public">
            <input type="checkbox" id="chart-public" name="chart-public">
            Make diagram public
          </label>
        </div>

        <div class="editor-container">
          <div class="editor-panel">
            <h4>Mermaid Syntax</h4>
            <textarea id="chart-content" class="mermaid-editor" required>graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    D --> B</textarea>
          </div>
          <div class="preview-panel">
            <h4>Preview</h4>
            <div id="chart-preview" class="mermaid-preview"></div>
          </div>
        </div>

        <div class="form-actions">
          <button type="submit" class="nui-button primary">Save</button>
          <button type="button" id="cancel-edit" class="nui-button secondary">Cancel</button>
        </div>
      </form>
    </section>

    <section id="charts-list" class="charts-list">
      <div id="no-charts-message" style="display: none;">
        <p>You don't have any diagrams yet. Create your first one!</p>
      </div>

      <div id="charts-grid" class="charts-grid">
        <!-- Charts will be loaded here dynamically -->
      </div>
    </section>
  </main>

  <footer>
    <div class="container">
      <p>&copy; 2023 Mermadic. All rights reserved.</p>
    </div>
  </footer>

  <script src="/js/mermaid.min.js"></script>
  <script src="/nui/nui.js"></script>
  <script src="/js/auth.js"></script>
  <script src="/js/charts.js"></script>
  <script src="/js/zoom-controls.js"></script>
  <script>
    // Initialize Mermaid with better error handling
    mermaid.initialize({
      startOnLoad: false,
      securityLevel: 'loose',
      theme: 'default',
      logLevel: 'fatal', // Only show fatal errors
      fontFamily: 'monospace',
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
        curve: 'linear' // Use linear curves which are more reliable
      },
      er: {
        useMaxWidth: true
      },
      sequence: {
        useMaxWidth: true,
        wrap: true,
        showSequenceNumbers: false
      },
      gantt: {
        useMaxWidth: true
      },
      journey: {
        useMaxWidth: true
      },
      // Suppress errors in the console
      suppressErrors: true
    });

    document.addEventListener('DOMContentLoaded', function() {
      // Check if user is logged in via localStorage
      let user = JSON.parse(localStorage.getItem('user'));

      // If no user in localStorage, check if we're coming from Google auth
      if (!user) {
        console.log('No user in localStorage, checking session...');
        // Fetch current user from session
        fetchCurrentUser()
          .then(sessionUser => {
            if (sessionUser) {
              // Store user in localStorage
              localStorage.setItem('user', JSON.stringify(sessionUser));
              user = sessionUser;
              console.log('User found in session, stored in localStorage:', user);
              // Load user's charts
              loadUserCharts();
            } else {
              // No user in session either, redirect to login
              window.location.href = '/login.html';
            }
          })
          .catch(error => {
            console.error('Error fetching current user:', error);
            window.location.href = '/login.html';
          });
      } else {
        // User already in localStorage, but verify session is still valid
        verifySession()
          .then(() => {
            // Session is valid, load charts
            loadUserCharts();
          })
          .catch(error => {
            console.error('Error verifying session:', error);
            // Try to load charts anyway
            loadUserCharts();
          });
      }

      // Setup event listeners
      document.getElementById('create-new-chart').addEventListener('click', function() {
        showChartEditor(true);
      });
      document.getElementById('cancel-edit').addEventListener('click', hideChartEditor);
      document.getElementById('chart-content').addEventListener('input', updateChartPreview);
      document.getElementById('chart-form').addEventListener('submit', saveChart);

      // Setup modal event listeners
      document.getElementById('close-view-modal').addEventListener('click', hideViewModal);
      document.getElementById('close-view-button').addEventListener('click', hideViewModal);
      document.getElementById('view-edit-chart').addEventListener('click', function() {
        const chartId = this.getAttribute('data-id');
        hideViewModal();
        editChart(chartId);
      });
      document.getElementById('view-delete-chart').addEventListener('click', function() {
        const chartId = this.getAttribute('data-id');
        hideViewModal();
        deleteChart(chartId);
      });
      document.getElementById('view-share-chart').addEventListener('click', function() {
        const shareId = this.getAttribute('data-id');
        shareChart(shareId);
      });

      // Initial preview update
      updateChartPreview();
    });

    function showChartEditor(isNew = true) {
      document.getElementById('charts-list').style.display = 'none';
      document.getElementById('chart-editor').style.display = 'block';

      if (isNew) {
        document.getElementById('editor-title').textContent = 'Create New Diagram';
        document.getElementById('chart-form').reset();
        document.getElementById('chart-id').value = '';
      }

      // Remove any existing zoom controls before showing editor
      const preview = document.getElementById('chart-preview');
      if (preview && preview.parentNode) {
        const existingControls = preview.parentNode.querySelectorAll('.zoom-controls');
        existingControls.forEach(control => control.remove());
      }
      updateChartPreview();
    }

    function hideChartEditor() {
      document.getElementById('chart-editor').style.display = 'none';
      document.getElementById('charts-list').style.display = 'block';

      // Clean up any zoom controls when hiding the editor
      const preview = document.getElementById('chart-preview');
      if (preview && preview.parentNode) {
        const existingControls = preview.parentNode.querySelectorAll('.zoom-controls');
        existingControls.forEach(control => control.remove());
      }
    }

    function updateChartPreview() {
      const content = document.getElementById('chart-content').value;
      const preview = document.getElementById('chart-preview');

      try {
        // Clear the preview div
        preview.innerHTML = '';

        // Remove any existing zoom controls for the preview
        const previewParent = preview.parentNode;
        if (previewParent) {
          const existingControls = previewParent.querySelectorAll('.zoom-controls');
          existingControls.forEach(control => control.remove());
        }

        // Generate a unique ID for the diagram
        const id = 'mermaid-diagram-' + Date.now();

        // Create a container for the rendered diagram
        const container = document.createElement('div');
        container.id = id;
        preview.appendChild(container);

        // Use the render API which is more stable
        try {
          // First try to parse the diagram to catch syntax errors
          mermaid.parse(content);

          // If parsing succeeds, render the diagram
          mermaid.render(id, content)
            .then(result => {
              // Success - insert the rendered SVG
              container.innerHTML = result.svg;

              // Add zoom controls directly
              const zoomControls = document.createElement('div');
              zoomControls.className = 'zoom-controls';
              zoomControls.innerHTML = `
                <button class="zoom-out" title="Zoom Out">-</button>
                <button class="zoom-reset" title="Reset Zoom">↺</button>
                <button class="zoom-in" title="Zoom In">+</button>
              `;

              // Insert zoom controls before the preview
              preview.parentNode.insertBefore(zoomControls, preview);

              // Make container zoomable
              preview.classList.add('zoomable-container');
              container.classList.add('zoomable-content');

              // Set current zoom level
              let currentZoom = 1.0;

              // Get zoom control buttons
              const zoomInBtn = zoomControls.querySelector('.zoom-in');
              const zoomOutBtn = zoomControls.querySelector('.zoom-out');
              const zoomResetBtn = zoomControls.querySelector('.zoom-reset');

              // Add event listeners
              zoomInBtn.addEventListener('click', () => {
                if (currentZoom < 3.0) {
                  currentZoom += 0.25;
                  updateZoom();
                }
              });

              zoomOutBtn.addEventListener('click', () => {
                if (currentZoom > 0.5) {
                  currentZoom -= 0.25;
                  updateZoom();
                }
              });

              zoomResetBtn.addEventListener('click', () => {
                currentZoom = 1.0;
                updateZoom();
              });

              // Update zoom level
              function updateZoom() {
                container.style.transform = `scale(${currentZoom})`;
                container.style.transformOrigin = 'center center';

                // Update button states
                zoomInBtn.disabled = currentZoom >= 3.0;
                zoomOutBtn.disabled = currentZoom <= 0.5;

                console.log('Current zoom level:', currentZoom);
              }
            })
            .catch(error => {
              console.error('Error rendering Mermaid diagram:', error);
              preview.innerHTML = `<div class="error-message">
                <strong>Error rendering diagram:</strong><br>
                ${error.message || 'Unknown error'}<br><br>
                <strong>Common issues to check:</strong><br>
                - Indentation and whitespace<br>
                - Special characters (try using simple ASCII characters)<br>
                - Missing connections or closing brackets<br>
                - Subgraph syntax (ensure proper nesting)<br>
              </div>`;
            });
        } catch (parseError) {
          // Handle syntax errors
          console.error('Mermaid parse error:', parseError);
          preview.innerHTML = `<div class="error-message">
            <strong>Mermaid Syntax Error:</strong><br>
            ${parseError.message || parseError}<br><br>
            <strong>Common issues to check:</strong><br>
            - Indentation and whitespace<br>
            - Special characters (try using simple ASCII characters)<br>
            - Missing connections or closing brackets<br>
            - Subgraph syntax (ensure proper nesting)<br>
          </div>`;
        }
      } catch (error) {
        console.error('Error setting up Mermaid diagram:', error);
        preview.innerHTML = `<div class="error-message">
          <strong>Error setting up diagram:</strong><br>
          ${error.message}<br><br>
          <strong>Common issues to check:</strong><br>
          - Indentation and whitespace<br>
          - Special characters (try using simple ASCII characters)<br>
          - Missing connections or closing brackets<br>
          - Subgraph syntax (ensure proper nesting)<br>
        </div>`;
      }
    }

    async function loadUserCharts() {
      try {
        // Use the getAllCharts function from charts.js to get all charts
        const charts = await getAllCharts();
        const chartsGrid = document.getElementById('charts-grid');
        const noChartsMessage = document.getElementById('no-charts-message');

        chartsGrid.innerHTML = '';

        if (charts && charts.length > 0) {
          noChartsMessage.style.display = 'none';

          charts.forEach(chart => {
            const chartCard = document.createElement('div');
            chartCard.className = 'chart-card';

            // Create the card header with title
            const cardHeader = document.createElement('h4');
            cardHeader.textContent = chart.title;
            chartCard.appendChild(cardHeader);

            // Create the chart preview container with click handler for viewing
            const previewContainer = document.createElement('div');
            previewContainer.className = 'chart-preview-small';
            previewContainer.style.cursor = 'pointer';
            previewContainer.title = 'Click to view diagram';
            previewContainer.addEventListener('click', function() {
              viewChart(chart.id);
            });

            // Create a container for the rendered diagram
            const containerId = 'chart-preview-' + chart.id;
            previewContainer.id = containerId;
            chartCard.appendChild(previewContainer);

            // Create a div with the mermaid class
            const mermaidDiv = document.createElement('div');
            mermaidDiv.className = 'mermaid';
            mermaidDiv.textContent = chart.content;
            previewContainer.appendChild(mermaidDiv);

            // Use setTimeout to ensure the DOM is fully updated before rendering
            setTimeout(() => {
              try {
                // Initialize Mermaid on the new element
                mermaid.init(undefined, mermaidDiv);
              } catch (error) {
                console.error('Error rendering chart preview:', error);
                previewContainer.innerHTML = '<div class="error-message">Error rendering diagram</div>';
              }
            }, 100);

            // Create the actions container
            const actionsContainer = document.createElement('div');
            actionsContainer.className = 'chart-actions';
            actionsContainer.innerHTML = `
              <button class="nui-button small view-chart" data-id="${chart.id}">View</button>
              <button class="nui-button small edit-chart" data-id="${chart.id}">Edit</button>
              <button class="nui-button small delete-chart" data-id="${chart.id}">Delete</button>
              <button class="nui-button small share-chart" data-id="${chart.share_id}">Share</button>
            `;
            chartCard.appendChild(actionsContainer);

            chartsGrid.appendChild(chartCard);
          });

          // Each chart is rendered individually using the render API

          // Add event listeners to buttons
          document.querySelectorAll('.view-chart').forEach(button => {
            button.addEventListener('click', function() {
              viewChart(this.getAttribute('data-id'));
            });
          });

          document.querySelectorAll('.edit-chart').forEach(button => {
            button.addEventListener('click', function() {
              editChart(this.getAttribute('data-id'));
            });
          });

          document.querySelectorAll('.delete-chart').forEach(button => {
            button.addEventListener('click', function() {
              deleteChart(this.getAttribute('data-id'));
            });
          });

          document.querySelectorAll('.share-chart').forEach(button => {
            button.addEventListener('click', function() {
              shareChart(this.getAttribute('data-id'));
            });
          });
        } else {
          noChartsMessage.style.display = 'block';
        }
      } catch (error) {
        console.error('Error loading charts:', error);
      }
    }

    async function saveChart(e) {
      e.preventDefault();

      const chartId = document.getElementById('chart-id').value;
      const title = document.getElementById('chart-title').value;
      const content = document.getElementById('chart-content').value;
      const isPublic = document.getElementById('chart-public').checked;

      try {
        let url = '/api/charts';
        let method = 'POST';

        if (chartId) {
          url = `/api/charts/${chartId}`;
          method = 'PUT';
        }

        const response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ title, content, isPublic })
        });

        if (!response.ok) {
          throw new Error('Failed to save chart');
        }

        hideChartEditor();
        loadUserCharts();
      } catch (error) {
        console.error('Error saving chart:', error);
      }
    }

    async function editChart(chartId) {
      try {
        console.log('Editing chart with ID:', chartId);
        const chart = await getChartById(chartId);
        console.log('Chart data received:', chart);

        if (!chart) {
          alert('Chart not found');
          return;
        }

        // Make sure we're getting the correct data structure
        const chartData = chart.chart || chart;
        console.log('Chart data to use:', chartData);

        // Do NOT reset the form here, just set the values
        document.getElementById('chart-id').value = chartData.id;
        document.getElementById('chart-title').value = chartData.title;
        document.getElementById('chart-content').value = chartData.content;
        document.getElementById('chart-public').checked = chartData.public === 1;
        document.getElementById('editor-title').textContent = 'Edit Diagram';

        showChartEditor(false);

        // Remove any existing zoom controls before updating preview
        const preview = document.getElementById('chart-preview');
        if (preview && preview.parentNode) {
          const existingControls = preview.parentNode.querySelectorAll('.zoom-controls');
          existingControls.forEach(control => control.remove());
        }

        updateChartPreview();
      } catch (error) {
        console.error('Error editing chart:', error);
      }
    }

    async function deleteChart(chartId) {
      if (!confirm('Are you sure you want to delete this diagram?')) {
        return;
      }

      try {
        const response = await fetch(`/api/charts/${chartId}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to delete chart');
        }

        loadUserCharts();
      } catch (error) {
        console.error('Error deleting chart:', error);
      }
    }

    function shareChart(shareId) {
      const shareUrl = `${window.location.origin}/share.html?id=${shareId}`;

      // Create a temporary input to copy the URL
      const tempInput = document.createElement('input');
      tempInput.value = shareUrl;
      document.body.appendChild(tempInput);
      tempInput.select();
      document.execCommand('copy');
      document.body.removeChild(tempInput);

      alert('Share URL copied to clipboard: ' + shareUrl);
    }

    // View chart in modal
    async function viewChart(chartId) {
      try {
        console.log('Viewing chart with ID:', chartId);
        // Get the chart data
        const chart = await getChartById(chartId);
        console.log('Chart data received for view:', chart);

        if (!chart) {
          alert('Chart not found');
          return;
        }

        // Make sure we're getting the correct data structure
        const chartData = chart.chart || chart;
        console.log('Chart data to use for view:', chartData);

        // Set the chart title
        document.getElementById('view-chart-title').textContent = chartData.title;

        // Set data attributes for the action buttons
        document.getElementById('view-edit-chart').setAttribute('data-id', chartData.id);
        document.getElementById('view-delete-chart').setAttribute('data-id', chartData.id);
        document.getElementById('view-share-chart').setAttribute('data-id', chartData.share_id);

        // Show the modal first to ensure the container is visible
        document.getElementById('view-chart-modal').style.display = 'block';

        // Get the container
        const container = document.getElementById('view-chart-container');
        container.innerHTML = '';

        // Remove any existing zoom controls for the view modal
        const containerParent = container.parentNode;
        if (containerParent) {
          const existingControls = containerParent.querySelectorAll('.zoom-controls');
          existingControls.forEach(control => control.remove());
        }

        // Create a div with the mermaid class
        const mermaidDiv = document.createElement('div');
        mermaidDiv.className = 'mermaid';
        mermaidDiv.textContent = chartData.content;
        container.appendChild(mermaidDiv);

        // Use setTimeout to ensure the DOM is fully updated before rendering
        setTimeout(() => {
          try {
            // Initialize Mermaid on the new element
            mermaid.init(undefined, mermaidDiv);

            // Add zoom controls directly
            const zoomControls = document.createElement('div');
            zoomControls.className = 'zoom-controls';
            zoomControls.innerHTML = `
              <button class="zoom-out" title="Zoom Out">-</button>
              <button class="zoom-reset" title="Reset Zoom">↺</button>
              <button class="zoom-in" title="Zoom In">+</button>
            `;

            // Insert zoom controls before the container
            container.parentNode.insertBefore(zoomControls, container);

            // Make container zoomable
            container.classList.add('zoomable-container');
            mermaidDiv.classList.add('zoomable-content');

            // Set current zoom level
            let currentZoom = 1.0;

            // Get zoom control buttons
            const zoomInBtn = zoomControls.querySelector('.zoom-in');
            const zoomOutBtn = zoomControls.querySelector('.zoom-out');
            const zoomResetBtn = zoomControls.querySelector('.zoom-reset');

            // Add event listeners
            zoomInBtn.addEventListener('click', () => {
              if (currentZoom < 3.0) {
                currentZoom += 0.25;
                updateZoom();
              }
            });

            zoomOutBtn.addEventListener('click', () => {
              if (currentZoom > 0.5) {
                currentZoom -= 0.25;
                updateZoom();
              }
            });

            zoomResetBtn.addEventListener('click', () => {
              currentZoom = 1.0;
              updateZoom();
            });

            // Update zoom level
            function updateZoom() {
              mermaidDiv.style.transform = `scale(${currentZoom})`;
              mermaidDiv.style.transformOrigin = 'center center';

              // Update button states
              zoomInBtn.disabled = currentZoom >= 3.0;
              zoomOutBtn.disabled = currentZoom <= 0.5;

              console.log('Current zoom level:', currentZoom);
            }
          } catch (error) {
            console.error('Error rendering diagram in modal:', error);
            container.innerHTML = `<div class="error-message">
              <strong>Error rendering diagram:</strong><br>
              ${error.message || 'Unknown error'}<br><br>
              <strong>Common issues to check:</strong><br>
              - Indentation and whitespace<br>
              - Special characters (try using simple ASCII characters)<br>
              - Missing connections or closing brackets<br>
              - Subgraph syntax (ensure proper nesting)<br>
            </div>`;
          }
        }, 100);
      } catch (error) {
        console.error('Error viewing chart:', error);
        alert('Error viewing chart: ' + error.message);
      }
    }

    // Hide the view modal
    function hideViewModal() {
      document.getElementById('view-chart-modal').style.display = 'none';

      // Clean up any zoom controls when hiding the modal
      const container = document.getElementById('view-chart-container');
      if (container && container.parentNode) {
        const existingControls = container.parentNode.querySelectorAll('.zoom-controls');
        existingControls.forEach(control => control.remove());
      }
    }

    // Function to fetch current user from session
    async function fetchCurrentUser() {
      try {
        const response = await fetch('/api/users/me');
        if (!response.ok) {
          if (response.status === 401) {
            // Not authenticated
            return null;
          }
          throw new Error('Failed to fetch current user');
        }

        const data = await response.json();
        return data.user;
      } catch (error) {
        console.error('Error fetching current user:', error);
        return null;
      }
    }
  </script>
</body>
</html>
