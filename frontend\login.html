<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login - Mermadic</title>
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/nui/nui.css">
</head>
<body>
  <header>
    <div class="container">
      <h1>Mermadic</h1>
      <nav>
        <ul>
          <li><a href="/">Home</a></li>
          <li><a href="/login.html" class="active">Login</a></li>
          <li><a href="/register.html">Register</a></li>
        </ul>
      </nav>
    </div>
  </header>

  <main class="container">
    <section class="auth-form">
      <h2>Login to Your Account</h2>
      <div id="error-message" class="error-message"></div>

      <form id="login-form">
        <div class="form-group">
          <label for="username">Username</label>
          <input type="text" id="username" name="username" class="nui-input" required>
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" id="password" name="password" class="nui-input" required>
        </div>

        <div class="form-actions">
          <button type="submit" class="nui-button primary">Login</button>
        </div>
      </form>

      <div class="social-login">
        <p>Or login with:</p>
        <a href="/api/auth/google" class="nui-button google-btn">
          <img src="/img/google-icon.svg" alt="Google" width="20" height="20">
          Login with Google
        </a>
      </div>

      <p class="auth-redirect">
        Don't have an account? <a href="/register.html">Register here</a>
      </p>
    </section>
  </main>

  <footer>
    <div class="container">
      <p>&copy; 2023 Mermadic. All rights reserved.</p>
    </div>
  </footer>

  <script src="/nui/nui.js"></script>
  <script src="/js/auth.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const loginForm = document.getElementById('login-form');
      const errorMessage = document.getElementById('error-message');

      loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        try {
          const response = await fetch('/api/users/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.message || 'Login failed');
          }

          // Store user in localStorage
          localStorage.setItem('user', JSON.stringify(data.user));

          // Redirect to dashboard
          window.location.href = '/dashboard.html';
        } catch (error) {
          errorMessage.textContent = error.message;
          errorMessage.style.display = 'block';
        }
      });
    });
  </script>
</body>
</html>
